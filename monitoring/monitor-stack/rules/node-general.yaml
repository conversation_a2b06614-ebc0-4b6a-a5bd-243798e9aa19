apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  labels:
    app: kube-prometheus-stack
    app.kubernetes.io/instance: kube-prometheus-stack
    release: kube-prometheus-stack
  name: general.rules
  namespace: monitoring
spec:
  groups:
    - name: general-alerts
      rules:
        # Alert: Node Exporter Down
        - alert: NodeExporterDown
          expr: up{job="node-exporter"} == 0
          for: 5m
          labels:
            severity: critical
          annotations:
            summary: "Node Exporter is down on {{ $labels.instance }}"
            description: "Node exporter is not responding for instance {{ $labels.instance }}."

        # Alert: High CPU Usage
        - alert: HighCpuUsage
          expr: 100 - (avg by (instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High CPU usage detected on {{ $labels.instance }}"
            description: "CPU usage is above 80% for the past 5 minutes on {{ $labels.instance }}."

        # Alert: Low Disk Space
        - alert: LowDiskSpace
          expr: node_filesystem_free_bytes / node_filesystem_size_bytes < 0.25
          for: 10m
          labels:
            severity: critical
          annotations:
            summary: "Low disk space on {{ $labels.instance }}"
            description: "Disk space is below 25% on {{ $labels.instance }} ({{ $labels.mountpoint }})."

        # Alert: High Memory Usage
        - alert: HighMemoryUsage
          expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.8
          for: 5m
          labels:
            severity: warning
          annotations:
            summary: "High memory usage detected on {{ $labels.instance }}"
            description: "Memory usage is above 80% for the past 5 minutes on {{ $labels.instance }}."