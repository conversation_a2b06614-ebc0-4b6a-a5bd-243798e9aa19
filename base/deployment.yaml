apiVersion: apps/v1
kind: Deployment
metadata:
  name: app
spec:
  selector:
    matchLabels:
      app: app
  template:
    metadata:
      labels:
        app: app
    spec:
      imagePullSecrets:
      - name: dockerhub-secret    # Add this
      containers:
      - name: app
        image: placeholder:latest
        env:
        - name: SERVICE_NAME
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: SERVICE_NAME
        - name: APP_TYPE
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: APP_TYPE
        - name: CONFIG_REMOTE_KEYS
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: CONFIG_REMOTE_KEYS
        ports:
        - containerPort: 8080