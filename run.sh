#!/bin/bash

# Function to validate kustomize builds
validate_kustomize() {
    local service=$1
    echo "🔍 Validating kustomize build for $service..."
    if ! kubectl kustomize build overlays/dev/apis/$service; then
        echo "❌ Kustomize build failed for $service"
        return 1
    fi
    echo "✅ Kustomize build successful for $service"
    return 0
}

# Function to deploy service
deploy_service() {
    local service=$1
    echo "🚀 Deploying $service..."
    
    # Validate kustomize first
    if ! validate_kustomize $service; then
        return 1
    fi

    # Apply Argo CD application
    if ! kubectl apply -f argocd/apps/dev.yaml; then
        echo "❌ Failed to deploy $service"
        return 1
    fi
    
    echo "✅ Deployment initiated for $service"
    return 0
}

# Main execution
case "$1" in
    "validate")
        validate_kustomize api-gateway
        ;;
    "deploy")
        deploy_service api-gateway
        ;;
    "preview")
        kustomize build overlays/dev/apis/api-gateway
        ;;
    *)
        echo "Usage: $0 {validate|deploy|preview}"
        exit 1
        ;;
esac