apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-ingress
  namespace: staging
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "order-api,facebook-bot"
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    # Order socket specific rewrite
    nginx.ingress.kubernetes.io/rewrite-target: /order/graphql
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api-staging.agbiz.vn
    secretName: api-staging-tls
  rules:
  - host: api-staging.agbiz.vn
    http:
      paths:
      - path: /order/socket
        pathType: Exact
        backend:
          service:
            name: order-api-app
            port:
              number: 3000