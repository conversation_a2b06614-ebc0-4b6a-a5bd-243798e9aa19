apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-staging-ingress
  namespace: staging
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    # WebSocket support
    nginx.ingress.kubernetes.io/websocket-services: "order-api,facebook-bot"
    # CORS for order endpoint
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-origin-regex: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # * does not works
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Accept-Language, Cache-Control, Content-Type, DNT, HostLocal, Referer, Sec-Ch-Ua, Sec-Ch-Ua-Mobile, Sec-Ch-Ua-Platform, Timezone, User-Agent, country-ids, project-ids, warehouse-ids"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"

    # Rewrite rules
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api-staging.agbiz.vn
    secretName: api-staging-tls
  rules:
  - host: api-staging.agbiz.vn
    http:
      paths:
      # WebSocket paths
      - path: /autoinbox/socket
        pathType: Exact
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 8080
      - path: /identity(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: identity-api-app
            port:
              number: 3000

      - path: /catalog(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: catalog-api-app
            port:
              number: 3000

      - path: /order(/|$)(?!socket)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: order-api-app
            port:
              number: 3000

      - path: /ffm-catalog(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: ffm-catalog-api-app
            port:
              number: 3000

      - path: /ffm-order(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: ffm-order-api-app
            port:
              number: 3000

      - path: /analytics(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: analytics-api-app
            port:
              number: 3000

      - path: /autoinbox(/|$)(?!socket)(.*)  # Modified to exclude socket path
        pathType: ImplementationSpecific
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 3000