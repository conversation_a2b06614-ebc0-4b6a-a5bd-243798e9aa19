apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>  # Replace with your email
    privateKeySecretRef:
      name: letsencrypt-prod-key
    solvers:
    - http01:
        ingress:
          class: nginx

--- 
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-dns01
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    privateKeySecretRef:
      name: letsencrypt-dns01-key
    solvers:
    - dns01:
        cloudflare:
          apiTokenSecretRef:
            name: cloudflare-api-token
            key: api-token # kubectl create secret generic cloudflare-api-token --from-literal=api-token=<key>  --namespace cert-manager
      selector:
        dnsZones:
          - "agbiz.tech"

---
# Certificate for wildcard domain
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: agbiz-all-tls-cert
  namespace: staging
spec:
  secretName: agbiz-all-tls-cert
  commonName: "*.agbiz.tech"
  dnsNames:
    - "*.agbiz.tech"
    - "agbiz.tech"  # Include apex domain
  issuerRef:
    name: letsencrypt-dns01  # Using our new DNS-01 issuer
    kind: ClusterIssuer
  duration: 2160h # 90 days
  renewBefore: 360h # 15 days