apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: gateway-stg-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/client-body-buffer-size: 512k
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: Accept, Authorization, Accept-Language,
      Cache-Control, Content-Type, DNT, HostLocal, Referer, Sec-Ch-Ua, Sec-Ch-Ua-Mobile,
      Sec-Ch-Ua-Platform, Timezone, User-Agent, country-ids, project-ids, warehouse-ids
    nginx.ingress.kubernetes.io/cors-allow-methods: GET, PUT, POST, DELETE, PATCH,
      OPTIONS
    nginx.ingress.kubernetes.io/cors-allow-origin: '*'
    nginx.ingress.kubernetes.io/cors-allow-origin-regex: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: 100m
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - gateway-stg.agbiz.vn
    secretName: gateway-stg-tls  # Cert-manager will create this
  rules:
  - host: gateway-stg.agbiz.vn
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-api-app
            port:
              number: 8080