# For RabbitMQ Management UI (Public)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: public-ingress
  namespace: db
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - rabbitmq.agbiz.tech
    secretName: agbiz-tech-tls-cert
  rules:
  - host: rabbitmq.agbiz.tech
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: rabbitmq
            port:
              number: 15672

---
# For Consul UI (Private with Basic Auth)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: private-ingress
  namespace: db
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"  # Changed to false to prevent loops
    nginx.ingress.kubernetes.io/auth-type: "basic"
    nginx.ingress.kubernetes.io/auth-secret: "consul-basic-auth"
    nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Consul"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - consul.agbiz.tech
    secretName: consul-tls-cert
  rules:
  - host: consul.agbiz.tech
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: consul
            port:
              number: 8500

---
# For Seq log (Private with Basic Auth)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: private-ingress
  namespace: staging
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"  # Changed to false to prevent loops
    # nginx.ingress.kubernetes.io/auth-type: "basic"
    # nginx.ingress.kubernetes.io/auth-secret: "seq-basic-auth"
    # nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Seq"
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - seq.agbiz.tech
    secretName: seq-tls-cert
  rules:
  - host: seq.agbiz.tech
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: seq
            port:
              number: 80
---
# For Wildcard Domain (*.agbiz.tech)
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: all-domain-ingress
  namespace: staging
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-dns01  # Using new issuer
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-set-headers: "user-id-headers"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "*.agbiz.tech"
    secretName: agbiz-all-tls-cert
  rules:
  - host: "root.agbiz.tech"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-root-app
            port:
              number: 3000
  - host: "*.agbiz.tech"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-admin-app
            port:
              number: 3000