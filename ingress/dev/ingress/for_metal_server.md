kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.12.0-beta.0/deploy/static/provider/baremetal/deploy.yaml
-> for nginx ingress
kubectl apply -f https://raw.githubusercontent.com/metallb/metallb/main/config/manifests/metallb-native.yaml
-> for load balancer


# Go to Cloudflare Dashboard:

Profile → API Tokens → Create Token
Use "Custom Token" template
Token name: "cert-manager-dns01"
Permissions:

Zone - DNS - Edit
Zone - Zone - Read


Zone Resources: Include - Specific zone - agbiz.tech
TTL: Set as needed (you can start with no expiration)


Create the Kubernetes secret for the token:

bashCopykubectl create secret generic cloudflare-api-token \
  --from-literal=api-token=your-cloudflare-token \
  --namespace cert-manager