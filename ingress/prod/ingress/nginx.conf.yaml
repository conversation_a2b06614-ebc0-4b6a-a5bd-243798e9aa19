apiVersion: v1
data:
  allow-snippet-annotations: "false"
  large-client-header-buffers: "8 16k"
kind: ConfigMap
metadata:
  annotations:
    meta.helm.sh/release-name: ingress-nginx
    meta.helm.sh/release-namespace: ingress-nginx
  creationTimestamp: "2024-12-19T09:35:56Z"
  labels:
    app.kubernetes.io/component: controller
    app.kubernetes.io/instance: ingress-nginx
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/part-of: ingress-nginx
    app.kubernetes.io/version: 1.11.2
    helm.sh/chart: ingress-nginx-4.11.2
  name: ingress-nginx-controller
  namespace: ingress-nginx
  resourceVersion: "4531"
  uid: deb7b54e-e267-45f9-96d0-a5e30fbff491