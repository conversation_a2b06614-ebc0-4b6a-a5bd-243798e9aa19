apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    cert-manager.io/issue-temporary-certificate: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"

    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-origin-regex: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # * does not works
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Accept-Language, Cache-Control, Content-Type, DNT, HostLocal, Referer, Sec-Ch-Ua, Sec-Ch-Ua-Mobile, Sec-Ch-Ua-Platform, Timezone, User-Agent, country-ids, project-ids, warehouse-ids"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
  name: api-gateway-ingress
  # namespace: production
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - gw.agbiz.vn
    secretName: api-gateway-production-tls
  rules:
  - host: "gw.agbiz.vn"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: gateway-api-app
            port:
              number: 8080