apiVersion: v1
kind: Secret
metadata:
  name: cloudflare-api-token-secret
  namespace: cert-manager
type: Opaque
stringData:
  api-token: # Enter your Cloudflare API token here

---
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: connect-wildcard-cert
  namespace: production    # Explicitly set to production namespace
spec:
  secretName: prod-connect-tls   # Changed secret name to be more specific
  dnsNames:
  - "*.connect.agbiz.vn"
  - "connect.agbiz.vn"
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-dns01
---