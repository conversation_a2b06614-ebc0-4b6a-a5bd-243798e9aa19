apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fb-webhook-production-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
   
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-origin-regex: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # * does not works
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Accept-Language, Cache-Control, Content-Type, DNT, HostLocal, Referer, Sec-Ch-Ua, Sec-Ch-Ua-Mobile, Sec-Ch-Ua-Platform, Timezone, User-Agent, country-ids, project-ids, warehouse-ids"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - fb.agbiz.vn
    secretName: agbiz-tls-secret  # TLS secret name
  rules:
  - host: fb.agbiz.vn
    http:
      paths:
      - path: /facebook/webhook
        pathType: Exact
        backend:
          service:
            name: secret-app
            port:
              number: 3000
      - path: /
        pathType: Prefix
        backend:
          service:
            name: secret-app
            port:
              number: 3000