apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: websocket-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    # nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    nginx.ingress.kubernetes.io/websocket-services: "order-api-app,facebook-bot-app"
    nginx.ingress.kubernetes.io/proxy-http-version: "1.1"
    # Order socket specific rewrite
    nginx.ingress.kubernetes.io/rewrite-target: /v2/order/graphql
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api.agbiz.vn
    secretName: api-production-tls
  rules:
  - host: api.agbiz.vn
    http:
      paths:
      - path: /v2/order/socket
        pathType: Exact
        backend:
          service:
            name: order-api-app
            port:
              number: 3000