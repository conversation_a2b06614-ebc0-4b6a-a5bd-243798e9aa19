apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: api-production-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "64k"
    nginx.ingress.kubernetes.io/proxy-buffers-number: "4"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    # WebSocket support
    nginx.ingress.kubernetes.io/websocket-services: "order-api-app,facebook-bot-app"
    # CORS for order endpoint
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, PUT, POST, DELETE, PATCH, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-origin-regex: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "*"
    # * does not works
    nginx.ingress.kubernetes.io/cors-allow-headers: "Accept, Authorization, Accept-Language, Cache-Control, Content-Type, DNT, HostLocal, Referer, Sec-Ch-Ua, Sec-Ch-Ua-Mobile, Sec-Ch-Ua-Platform, Timezone, User-Agent, country-ids, project-ids, warehouse-ids"
    nginx.ingress.kubernetes.io/cors-allow-credentials: "true"

    # Rewrite rules
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/use-regex: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - api.agbiz.vn
    secretName: api-production-tls
  rules:
  - host: api.agbiz.vn
    http:
      paths:
      # V1 WebSocket paths
      - path: /order/socket
        pathType: Exact
        backend:
          service:
            name: order-api-app
            port:
              number: 3000
      - path: /autoinbox/socket
        pathType: Exact
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 8080

      # V2 WebSocket paths
      # - path: /v2/order/socket
      #   pathType: Exact
      #   backend:
      #     service:
      #       name: order-api-app
      #       port:
      #         number: 3000
      - path: /v2/autoinbox/socket
        pathType: Exact
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 8080

      # V1 API paths
      - path: /identity(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: identity-api-app
            port:
              number: 3000
      - path: /facebook/webhook(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: identity-api-app
            port:
              number: 3000

      - path: /catalog(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: catalog-api-app
            port:
              number: 3000

      - path: /order(/|$)(?!socket)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: order-api-app
            port:
              number: 3000

      - path: /autoinbox(/|$)(?!socket)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 3000

      # V2 API paths
      - path: /v2/identity(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: identity-api-app
            port:
              number: 3000

      - path: /v2/order(/|$)(?!socket)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: order-api-app
            port:
              number: 3000

      - path: /v2/ffm-order(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: ffm-order-api-app
            port:
              number: 3000

      - path: /v2/ffm-catalog(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: ffm-catalog-api-app
            port:
              number: 3000

      - path: /v2/autoinbox(/|$)(?!socket)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: facebook-bot-app
            port:
              number: 3000

      - path: /v2/analytics(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: analytics-api-app
            port:
              number: 3000
      - path: /v2/catalog(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: catalog-api-app
            port:
              number: 3000
      - path: /v2/crawler(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: secret-app
            port:
              number: 3000