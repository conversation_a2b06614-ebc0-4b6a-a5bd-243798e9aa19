apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: wildcard-pfg-asia
  namespace: production
spec:
  secretName: wildcard-pfg-asia-tls   # Changed to unique name
  dnsNames:
  - "*.pfg.asia"
  issuerRef:
    kind: ClusterIssuer
    name: letsencrypt-dns01
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wildcard-pfg-asia-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-dns01"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "*.pfg.asia"
    secretName: wildcard-pfg-asia-tls
  rules:
  # Rule for *.connect.agbiz.vn
  - host: "*.pfg.asia"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-admin-app
            port:
              number: 3000