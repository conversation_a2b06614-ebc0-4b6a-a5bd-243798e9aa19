apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    cert-manager.io/issue-temporary-certificate: "true"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
  name: all-point-domains-ingress
  namespace: production
spec:
  ingressClassName: nginx
  rules:
  - http:
      paths:
      - backend:
          service:
            name: athena-admin-app
            port:
              number: 3000
        path: /
        pathType: Prefix
  tls:
  - secretName: default-tls