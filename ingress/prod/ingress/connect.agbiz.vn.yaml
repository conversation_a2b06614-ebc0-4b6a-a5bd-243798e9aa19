apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: connect-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-dns01"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "*.connect.agbiz.vn"
    - "connect.agbiz.vn"
    secretName: wildcard-connect-tls
  rules:
  # Rule for connect.agbiz.vn
  - host: "connect.agbiz.vn"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-admin-app
            port:
              number: 3000
  # Rule for *.connect.agbiz.vn
  - host: "*.connect.agbiz.vn"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-admin-app
            port:
              number: 3000