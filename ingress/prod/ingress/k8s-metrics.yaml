apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: kube-state-metrics-ingress
  namespace: monitoring
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    # nginx.ingress.kubernetes.io/auth-type: "basic"
    # nginx.ingress.kubernetes.io/auth-secret: "seq-basic-auth"
    # nginx.ingress.kubernetes.io/auth-realm: "Authentication Required - Seq"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - metrics.agbiz.tech
    secretName: metrics-server-tls  # Cert-manager will create this
  rules:
  - host: metrics.agbiz.tech
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: kube-prometheus-stack-grafana
            port:
              number: 80