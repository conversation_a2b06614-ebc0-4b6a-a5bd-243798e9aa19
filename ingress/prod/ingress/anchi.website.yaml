apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: anchi-website-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod  # Using new issuer
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-passthrough: "true"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-set-headers: "user-id-headers"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "anchi.website"
    secretName: anchi-website-tls-cert
  rules:
  - host: "anchi.website"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-admin-app
            port:
              number: 3000