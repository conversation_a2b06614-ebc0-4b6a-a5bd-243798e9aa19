apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: all-domain-ingress
  namespace: production
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-prod  # Using new issuer
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/proxy-buffer-size: "16k"
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "false"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/proxy-set-headers: "user-id-headers"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - "root.agbiz.vn"
    secretName: agbiz-root-tls-cert
  rules:
  - host: "root.agbiz.vn"
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: athena-root-app
            port:
              number: 3000