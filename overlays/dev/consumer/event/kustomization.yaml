apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: event-consumer-

commonLabels:
  app: event
  service-type: consumer
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=event
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,consumers/event-consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-event
  newTag: '216812c8-3'
