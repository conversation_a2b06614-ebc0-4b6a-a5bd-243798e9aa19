apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: binlog-consumer-

commonLabels:
  app: binlog
  service-type: consumer
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=binlog
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,database/postgres.toml,database/postgres_replication.toml,consumers/data-replica/binlog_consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-binlog
  newTag: 'cb06e650-4'
