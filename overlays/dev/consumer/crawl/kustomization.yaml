apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: crawl-consumer-

commonLabels:
  app: crawl
  service-type: consumer
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=crawl
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,consumers/crawl-consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-crawl
  newTag: 'ac2f4bb6-4'
