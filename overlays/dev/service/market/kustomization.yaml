apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: market-service-

commonLabels:
  app: market
  service-type: service
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=market
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,services/market.toml

images:
- name: placeholder
  newName: agbiz/go-service-market
  newTag: '552b770e-62'
