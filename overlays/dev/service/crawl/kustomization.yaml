apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: crawl-service-

commonLabels:
  app: crawl
  service-type: service
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=crawl
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,services/crawl.toml,database/redis.toml

images:
- name: placeholder
  newName: agbiz/go-service-crawl
  newTag: '6a52ba34-17'
