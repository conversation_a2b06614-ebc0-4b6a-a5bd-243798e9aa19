apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: user-service-

commonLabels:
  app: user
  service-type: service
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=user
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,database/redis.toml,services/user_service.toml

images:
- name: placeholder
  newName: agbiz/go-service-user
  newTag: '552b770e-12'
