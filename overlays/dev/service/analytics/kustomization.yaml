apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: analytics-service-

commonLabels:
  app: analytics
  service-type: service
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=analytics
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,services/analytics.toml

images:
- name: placeholder
  newName: agbiz/go-service-analytics
  newTag: '556f63a5-30'
