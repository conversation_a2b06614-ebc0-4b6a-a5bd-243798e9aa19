apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: marketing-service-

commonLabels:
  app: marketing
  service-type: service
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=marketing
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,database/redis.toml,services/marketing_service.toml

images:
- name: placeholder
  newName: agbiz/go-service-marketing
  newTag: '6d80b2af-5'
