# helm upgrade --install vector vector/vector -n logging --create-namespace -f overlays/dev/vector.dev.yaml
role: "Agent"
service:
  enabled: false  # Disable Service to avoid previous port error
dataDir: "/vector-data-dir"  # Align with existing mount point
customConfig:
  sources:
    nginx_ingress_logs:
      type: "kubernetes_logs"
      extra_namespace_label_selector: "kubernetes.io/metadata.name=ingress-nginx"  # Filter to ingress-nginx namespace
      extra_label_selector: "app.kubernetes.io/name=ingress-nginx,app.kubernetes.io/component=controller"  # Filter to Nginx Ingress pods
      data_dir: "/vector-data-dir"  # Match the existing mount point
  transforms:
    parse_body_logs:
      type: "remap"
      inputs: ["nginx_ingress_logs"]
      source: |
        ignores = ["/v2/identity/users/login"]
        parsed, err = parse_regex(.message, r'^(?<remote_addr>\S+) - (?<remote_user>-|\w*) \[(?<time_local>[^\]]+)\] "(?<request_method>\w+) (?<request_uri>[^"]+) (?<http_version>[^"]+)" (?<status>\d+) (?<body_bytes_sent>\d+) "(?<http_referer>[^"]*)" "(?<http_user_agent>[^"]*)" (?<response_time>[\d\.]+) (?<request_time>[\d\.]+) Body: "(?<request_body>.*)"')
        if err == null {
            . = merge(., parsed)
            .message = .request_uri
            if includes(ignores, .request_uri) {
                .request_body = "hidden"
            }
        }
  sinks:
    victorialogs:
      type: "elasticsearch"
      inputs: ["parse_body_logs"]
      endpoints:
        - "http://log.do.agbiz.vn:9428/insert/elasticsearch/"  # Your updated VictoriaLogs endpoint
      mode: "bulk"
      query:
        _msg_field: "message"
        _time_field: "time"
        _stream_fields: "request,remote_addr"

    clickhouse_native:
      type: clickhouse
      inputs: ["parse_body_logs"]
      endpoint: http://clickhouse-pub.do.agbiz.vn:8123
      database: logs
      table: nginx_logs
      skip_unknown_fields: true
      auth:
        strategy: basic
        user: logger
        password: <pw>
resources:
  requests:
    memory: "64Mi"
    cpu: "100m"
  limits:
    memory: "256Mi"
    cpu: "500m"