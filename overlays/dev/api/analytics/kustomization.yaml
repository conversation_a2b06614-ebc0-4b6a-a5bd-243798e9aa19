apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: analytics-api-

commonLabels:
  app: analytics
  service-type: api
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=analytics
  - APP_TYPE=api
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,apis/analytics-api/config.toml

images:
- name: placeholder
  newName: agbiz/go-api-analytics
  newTag: '556f63a5-24'
