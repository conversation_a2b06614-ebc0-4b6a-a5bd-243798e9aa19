apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: users-api-

commonLabels:
  app: users
  service-type: api
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=users
  - APP_TYPE=api
  - CONFIG_REMOTE_KEYS=apis/user.toml

images:
- name: placeholder
  newName: agbiz/go-api-users
  newTag: '93f1f90c-9'
