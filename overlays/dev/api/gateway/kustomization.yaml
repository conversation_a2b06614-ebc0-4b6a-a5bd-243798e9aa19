apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: gateway-api-

commonLabels:
  app: gateway
  service-type: api
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=gateway
  - APP_TYPE=api
  - CONFIG_REMOTE_KEYS=apis/gateway.toml,database/redis.toml

images:
- name: placeholder
  newName: agbiz/go-api-gateway
  newTag: '552b770e-19'
