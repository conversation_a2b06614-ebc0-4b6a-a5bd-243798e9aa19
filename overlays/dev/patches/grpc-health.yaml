apiVersion: apps/v1
kind: Deployment
metadata:
  name: placeholder
  labels:
    component-type: api  # This ensures the patch only applies to resources with this label
spec:
  template:
    spec:
      containers:
        - name: app
          livenessProbe:
            tcpSocket:
              port: 6688
            periodSeconds: 10
            failureThreshold: 5
          readinessProbe:
            tcpSocket:
              port: 6688
            initialDelaySeconds: 30
            periodSeconds: 10
            failureThreshold: 3