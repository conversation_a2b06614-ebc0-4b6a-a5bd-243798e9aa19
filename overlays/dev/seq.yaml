apiVersion: v1
kind: PersistentVolume
metadata:
  name: seq-data-pv
  namespace: staging
spec:
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteOnce
  storageClassName: local-storage
  local:
    path: /data/seq-data
  nodeAffinity:
    required:
      nodeSelectorTerms:
        - matchExpressions:
            - key: kubernetes.io/hostname
              operator: In
              values:
                - vmi2282108.contaboserver.net
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: seq-data
  namespace: staging
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: local-storage
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: seq
  namespace: staging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: seq
  template:
    metadata:
      labels:
        app: seq
    spec:
      containers:
      - name: seq
        image: datalust/seq:2022.1
        resources:
          requests:
            cpu: "100m"
            memory: "128Mi"
          limits:
            cpu: "1200m"
            memory: "4096Mi"
        ports:
        - containerPort: 80
        env:
        - name: ACCEPT_EULA
          value: "Y"
        volumeMounts:
        - name: seq-data
          mountPath: /data
      volumes:
      - name: seq-data
        persistentVolumeClaim:
          claimName: seq-data
---
apiVersion: v1
kind: Service
metadata:
  name: seq
  namespace: staging
spec:
  ports:
  - port: 80
    targetPort: 80
  selector:
    app: seq
  type: ClusterIP
