# overlays/dev/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
# common
- consul.yaml
#- ingress.yaml
# APIs
- api/gateway
- api/users
- api/webhooks
- api/market
- api/analytics
# Services (GRPC)
- service/agsale
- service/user
- service/webhook
- service/marketing
- service/market
- service/crawl
- service/analytics
# Consumers
- consumer/binlog
- consumer/order
- consumer/webhook
- consumer/event
- consumer/crawl
# Schedulers
# - scheduler/report
# - scheduler/cleanup

commonLabels:
  environment: dev
  app.kubernetes.io/part-of: go-services

patches:
- path: patches/api-health.yaml
  target:
    kind: Deployment
    labelSelector: "service-type=api"
- path: patches/grpc-health.yaml
  target:
    kind: Deployment
    labelSelector: "service-type=service"

- path: patches/resources.yaml
  target:
    kind: Deployment
- path: patches/gateway-resources.yaml
  target:
    kind: Deployment
    name: gateway-api-app