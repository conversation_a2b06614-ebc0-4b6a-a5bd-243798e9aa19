# overlays/dev/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
# common
# APIs
- api/gateway
# - api/users
- api/webhooks
- api/analytics
- api/market
# Services (GRPC)
- service/agsale
- service/user
- service/webhook
- service/analytics
- service/market
- service/crawl
# Consumers
- consumer/binlog
- consumer/order
- consumer/webhook
- consumer/crawl
# Schedulers
# - scheduler/report
# - scheduler/cleanup

commonLabels:
  environment: production
  app.kubernetes.io/part-of: go-services

# patches:
# - path: patches/resources.yaml
#   target:
#     kind: Deployment
# - path: patches/gateway-resources.yaml
#   target:
#     kind: Deployment
#     name: gateway-api-app