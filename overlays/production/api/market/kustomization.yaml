apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: market-api-

commonLabels:
  app: market
  service-type: api
  environment: dev

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=market
  - APP_TYPE=api
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,apis/market-api/config.toml

images:
- name: placeholder
  newName: agbiz/go-api-market
  newTag: 'a8b80b1f-25'
