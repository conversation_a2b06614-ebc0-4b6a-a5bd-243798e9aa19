apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: webhooks-api-

commonLabels:
  app: webhooks
  service-type: api
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=webhooks
  - APP_TYPE=api
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,apis/webhook-api/config.toml

images:
- name: placeholder
  newName: agbiz/go-api-webhooks
  newTag: '25c26f09-5'
