apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: webhook-service-

commonLabels:
  app: webhook
  service-type: service
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=webhook
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,database/redis.toml,services/webhook_service.toml

images:
- name: placeholder
  newName: agbiz/go-service-webhook
  newTag: 'fa0847ca-4'
