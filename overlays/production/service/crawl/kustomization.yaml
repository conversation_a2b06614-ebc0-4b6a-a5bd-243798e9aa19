apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: crawl-service-

commonLabels:
  app: crawl
  service-type: service
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=crawl
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,database/redis.toml,services/crawl_service.toml

images:
- name: placeholder
  newName: agbiz/go-service-crawl
  newTag: 'f4708810-78'

patchesStrategicMerge:
  - ./patches/crawl-metric-service-port.yaml
  - ./patches/crawl-metric-deployment.yaml
