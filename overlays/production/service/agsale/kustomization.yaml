apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: agsale-service-

commonLabels:
  app: agsale
  service-type: service
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=agsale
  - APP_TYPE=service
  - CONFIG_REMOTE_KEYS=database/postgres.toml,database/redis.toml,services/agsale_service.toml

images:
- name: placeholder
  newName: agbiz/go-service-agsale
  newTag: 'eaa25ac3-7'
