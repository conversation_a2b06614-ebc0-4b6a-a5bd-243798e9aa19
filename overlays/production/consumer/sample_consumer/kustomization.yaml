apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: sample_consumer-consumer-

commonLabels:
  app: sample_consumer
  service-type: consumer
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=sample_consumer
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,consumers/sample-consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-sample_consumer
  newTag: '1'
