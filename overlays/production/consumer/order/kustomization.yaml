apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: order-consumer-

commonLabels:
  app: order
  service-type: consumer
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=order
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,consumers/order-consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-order
  newTag: '11515e4a-6'
