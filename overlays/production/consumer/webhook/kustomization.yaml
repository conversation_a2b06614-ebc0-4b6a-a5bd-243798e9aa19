apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../../../base

namePrefix: webhook-consumer-

commonLabels:
  app: webhook
  service-type: consumer
  environment: production

configMapGenerator:
- name: app-config
  behavior: merge
  literals:
  - SERVICE_NAME=webhook
  - APP_TYPE=consumer
  - CONFIG_REMOTE_KEYS=database/redis.toml,database/rabbitmq.toml,consumers/webhook-consumer.toml

images:
- name: placeholder
  newName: agbiz/go-consumer-webhook
  newTag: '6359c236-9'
