# Go Services Infrastructure

## Directory Structure
- base/: Base configurations for all services
- apps/: Service-specific configurations
  - api/: API services
  - service/: service services
  - consumer/: Consumer services
  - scheduler/: Scheduler services
- argocd/: Argo CD configurations

## Adding a New Service
1. install atcli (cd athena-go && go install pkg/toolkit/atcli.go)
2. atcli gen cicd <path_to_cicd_file>

## Deployment
The services will be automatically deployed by Argo CD with:
- Dev tag: your-registry/service:dev
- Prod tag: your-registry/service:prod
