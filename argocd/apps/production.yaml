apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: go-services-production
  namespace: argocd
  labels:
    environment: production
    app.kubernetes.io/part-of: go-services
  annotations:
    argocd.argoproj.io/sync-wave: "1"
spec:
  project: default
  source:
    repoURL: **************:a7923/go-svc/athena-deployments.git
    path: overlays/production
    targetRevision: HEAD
  destination:
    server: https://kubernetes.default.svc
    namespace: default
  syncPolicy:
    automated: {}