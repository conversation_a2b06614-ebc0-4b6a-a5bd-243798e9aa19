apiVersion: v1
kind: ConfigMap
metadata:
  name: argocd-cm
  namespace: argocd
data:
  applications.resourceFilter: |
    - groups:
        - name: go-services
          resources:
            - apis
            - services
            - consumers
            - schedulers
  resource.customizations.group.labels: |
    service-type=api: apis
    service-type=service: services
    service-type=consumer: consumers
    service-type=scheduler: schedulers